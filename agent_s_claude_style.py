#!/usr/bin/env python3
"""
Claude-Style Agent-S with Ollama - GPU Optimized
Conversational AI computer assistant
"""

import os
import pyautogui
import io
import time
from PIL import Image
import requests
import base64
import json

# Configuration
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_VISION_URL = f"{OLLAMA_BASE_URL}/api/chat"

# GPU Optimization Settings
OLLAMA_GPU_SETTINGS = {
    "num_gpu": -1,  # Use all available GPUs
    "gpu_layers": 50,  # Offload layers to GPU
    "main_gpu": 0,  # Primary GPU
    "tensor_split": None,  # Auto-split tensors
}

def take_screenshot():
    """Take and encode screenshot for AI analysis"""
    screenshot = pyautogui.screenshot()
    # Optimize size for faster processing
    screenshot = screenshot.resize((1280, 720), Image.LANCZOS)
    
    buffered = io.BytesIO()
    screenshot.save(buffered, format="JPEG", quality=90)
    img_bytes = buffered.getvalue()
    img_base64 = base64.b64encode(img_bytes).decode('utf-8')
    
    return img_base64

def call_ollama_vision(model, instruction, image_base64):
    """Call Ollama vision model with Claude-like conversation"""
    
    # Claude-style conversational prompt
    prompt = f"""You are Claude, an AI assistant that can see and control computers. You're helpful, harmless, and honest.

I need you to look at this screenshot and help me with: {instruction}

IMPORTANT: Look at the actual screenshot and identify visible UI elements and their approximate coordinates.

Your response should be in this format:
1. Describe what you can see on the screen (windows, taskbar, desktop, etc.)
2. Explain your step-by-step plan to accomplish the task
3. Provide working Python code using coordinates you can actually see

CRITICAL AUTOMATION RULES:
🚨 NEVER assume applications are already open - ALWAYS open them fresh!
🚨 NEVER use pyautogui.locateOnScreen() or pyautogui.getAllWindows() - these often fail!
🚨 ALWAYS look at the screenshot and use coordinates you can actually see!

FOR OPENING ANY APPLICATION ON WINDOWS:
```python
import pyautogui
import time

# Step 1: Click the Start button (usually bottom-left corner)
pyautogui.click(50, 1050)  # Adjust based on what you see in screenshot
time.sleep(1)

# Step 2: Type the application name
pyautogui.type('notepad')  # or 'calculator', 'browser', etc.
time.sleep(1)

# Step 3: Press Enter to launch
pyautogui.press('enter')
time.sleep(3)  # Wait for app to fully load

# Step 4: Now use the application
pyautogui.type('Hello World!')  # Example content
```

FOR CLICKING VISIBLE BUTTONS/ELEMENTS:
- Look at the screenshot carefully
- Estimate coordinates based on what you can see
- Click directly on visible elements
- Don't assume anything exists that you can't see

EXAMPLE RESPONSES:

For "open calculator":
"I can see the Windows desktop with a taskbar at the bottom. I'll click the Start button in the bottom-left corner, search for calculator, and open it."

For "close this window":
"I can see a window open in the center of the screen. I'll click the X button in the top-right corner of that window."

REMEMBER: 
- Use coordinates based on what you actually see in the image
- Always open applications from scratch using the Start menu
- Be specific about which elements you're targeting
- Include proper delays between actions

Now help me with: {instruction}"""
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": prompt,
                "images": [image_base64]
            }
        ],
        "stream": False,
        "options": {
            "temperature": 0.1,  # More focused responses
            "top_p": 0.9,
            "num_gpu": -1,  # Use all GPUs
            "gpu_layers": 50,  # Offload to GPU
            "num_thread": 8,  # Optimize CPU threads
        }
    }
    
    try:
        # Increased timeout for large models
        response = requests.post(OLLAMA_VISION_URL, json=payload, timeout=120)
        if response.status_code == 200:
            result = response.json()
            return result['message']['content']
        else:
            return f"API Error: {response.status_code} - {response.text}"
    except requests.exceptions.Timeout:
        return "⏱️ Response took too long. The model might be processing a complex request. Try a simpler command or use a smaller model."
    except Exception as e:
        return f"Connection Error: {e}"

def extract_python_code(text):
    """Extract Python code from Claude-style response"""
    # Check for completion messages
    if "task appears to be complete" in text.lower() or "already complete" in text.lower():
        return "DONE"
    
    # Look for code blocks
    if "```python" in text:
        start = text.find("```python") + 9
        end = text.find("```", start)
        if end != -1:
            return text[start:end].strip()
    
    if "```" in text:
        start = text.find("```") + 3
        end = text.find("```", start)
        if end != -1:
            return text[start:end].strip()
    
    # Look for pyautogui commands in the text
    lines = text.split('\n')
    python_lines = []
    for line in lines:
        if 'pyautogui' in line or 'time.sleep' in line:
            python_lines.append(line.strip())
    
    if python_lines:
        return '\n'.join(python_lines)
    
    return None

def select_optimal_model():
    """Select the best model based on system performance"""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            models_data = response.json()
            available_models = [m['name'] for m in models_data['models']]
            
            # Prioritize models by speed/performance balance
            preferred_models = [
                "llava:13b",      # Best balance
                "minicpm-v:8b",   # Fastest
                "qwen2.5vl:32b",  # Most capable but slow
                "llava:7b",       # Very fast
                "llava:34b"       # Slow but capable
            ]
            
            for model in preferred_models:
                if model in available_models:
                    return model
        
        return "llava:13b"  # Default fallback
    except:
        return "llava:13b"

def main():
    print("🤖 CLAUDE-STYLE AGENT-S + OLLAMA (GPU)")
    print("=" * 50)
    
    # Test Ollama connection
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code != 200:
            print("❌ Ollama not running. Start with: ollama serve")
            return
    except:
        print("❌ Cannot connect to Ollama. Start with: ollama serve")
        return
    
    # Get available models
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        models_data = response.json()
        available_models = [m['name'] for m in models_data['models']]
        
        print(f"\n📋 Available models ({len(available_models)} total):")
        vision_models = [m for m in available_models if any(x in m for x in ['llava', 'minicpm-v', 'qwen2.5vl'])]
        
        for i, model in enumerate(vision_models[:5], 1):  # Show top 5
            print(f"{i}. {model}")
    except:
        vision_models = ["llava:13b", "llava:7b", "minicpm-v:8b", "qwen2.5vl:32b"]
        print(f"\n📋 Default models:")
        for i, model in enumerate(vision_models, 1):
            print(f"{i}. {model}")
    
    # Model selection
    print(f"\n💡 Recommendation: Use llava:13b for best balance of speed/quality")
    choice = input("Select model (1-5) or press Enter for recommended: ").strip()
    
    if not choice:
        selected_model = select_optimal_model()
    else:
        try:
            selected_model = vision_models[int(choice) - 1]
        except:
            selected_model = select_optimal_model()
    
    print(f"\n🤖 Using model: {selected_model}")
    print(f"🎯 GPU Status: Checking optimization...")
    
    # Check GPU usage
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/ps", timeout=5)
        if response.status_code == 200:
            running_models = response.json()
            if running_models.get('models'):
                for model in running_models['models']:
                    if model['name'] == selected_model:
                        print(f"✅ Model loaded: {model['processor']}")
                        break
    except:
        print("🔄 GPU status check unavailable")
    
    print(f"\n🎪 Welcome! I'm Claude, your AI computer assistant.")
    print(f"💬 I can see your screen and help you with computer tasks.")
    print(f"🚀 Just tell me what you'd like to do in natural language!")
    
    # Interactive conversation loop
    while True:
        print("\n" + "💬 " + "="*48)
        print("What would you like me to help you with?")
        print("Examples:")
        print("  • 'Open calculator and calculate 15 * 25'")
        print("  • 'Take a screenshot and save it'")
        print("  • 'Close all open browser tabs'")
        print("  • 'Open notepad and write a shopping list'")
        print("  • 'quit' to exit")
        
        instruction = input("\n🗣️  You: ").strip()
        
        if instruction.lower() in ['quit', 'exit', 'q', 'bye']:
            print("\n👋 Claude: Goodbye! It was great helping you automate your computer tasks!")
            break
        
        if not instruction:
            continue
        
        print(f"\n🤖 Claude: Let me help you with that! Give me a moment to see your screen...")
        
        # Take screenshot
        try:
            start_time = time.time()
            image_base64 = take_screenshot()
            screenshot_time = time.time() - start_time
            print(f"📸 Screenshot captured ({screenshot_time:.1f}s)")
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
            continue
        
        # Get AI response
        print(f"🧠 Thinking... (this may take 30-60 seconds for large models)")
        try:
            start_time = time.time()
            response = call_ollama_vision(selected_model, instruction, image_base64)
            ai_time = time.time() - start_time
            
            print(f"\n🤖 Claude ({ai_time:.1f}s):")
            print("-" * 50)
            
            # Check for errors first
            if "Error:" in response or "timeout" in response.lower():
                print(f"❌ {response}")
                print("\n💡 Try using a smaller/faster model or a simpler command.")
                continue
            
            # Display Claude's response
            print(response)
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ AI processing failed: {e}")
            continue
        
        # Extract and execute code
        code = extract_python_code(response)
        
        if code == "DONE":
            print("\n✅ Claude: The task appears to be complete!")
            continue
        
        if not code:
            print("\n🤖 Claude: I provided information but no actions are needed.")
            continue
        
        print(f"\n💻 Code to execute:")
        print("```python")
        print(code)
        print("```")
        
        # Ask for permission
        execute = input("\n⚡ Execute this code? (y/n/edit): ").strip().lower()
        
        if execute == 'edit':
            print("📝 Edit the code above and run it manually if needed.")
            continue
        elif execute != 'y':
            print("⏭️ Skipped execution")
            continue
        
        # Execute code
        print("\n⚡ Executing...")
        try:
            # Safe execution environment
            exec_globals = {
                'pyautogui': pyautogui,
                'time': time,
                'print': print,
                'os': os,
                'Image': Image
            }
            
            exec(code, exec_globals)
            print("✅ Code executed successfully!")
            
        except Exception as e:
            print(f"❌ Execution failed: {e}")
            print("💡 The code might need manual adjustment.")
        
        time.sleep(0.5)  # Brief pause before next command

if __name__ == "__main__":
    main() 